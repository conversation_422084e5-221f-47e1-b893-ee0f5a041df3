package com.sdesrd.filetransfer.client.config;

import lombok.Data;

/**
 * 客户端配置类
 */
@Data
public class ClientConfig {
    
    /**
     * 认证配置（必填）。
     * 包含服务器地址、端口、协议（HTTP/HTTPS）和用户身份验证信息，用于构建请求时添加认证头。
     */
    private ClientAuthConfig auth = new ClientAuthConfig();
    
    /**
     * 分块大小（字节），默认2MB。
     * 将文件拆分为固定大小的片段进行上传或下载，每个片段作为独立 HTTP 请求发送。
     * 过小会产生过多请求开销，过大可能导致内存压力或重试代价增加。
     */
    private long chunkSize = 2 * 1024 * 1024L;
    
    /**
     * 连接超时时间（秒），默认30秒。
     * 在建立到服务器的 TCP 连接时，客户端等待对方响应的最长时间。
     * 超时后将抛出连接超时异常，避免网络故障或服务器不可达时长时间阻塞。
     */
    private long connectTimeoutSeconds = 30;
    
    /**
     * 读取超时时间（秒），默认60秒。
     * 在发送请求后，等待服务器返回数据时，两次数据包之间允许的最大间隔。
     * 超时后将抛出读取超时异常，防止因网络卡顿或服务器无响应而挂起。
     */
    private long readTimeoutSeconds = 60;
    
    /**
     * 写入超时时间（秒），默认60秒。
     * 在发送请求体（如文件分块）时，两次写入操作之间允许的最大间隔。
     * 超时后将抛出写入超时异常，防止上传过程因网络阻塞而卡死。
     */
    private long writeTimeoutSeconds = 60;
    
    /**
     * 最大并发传输数，默认3。
     * 同时进行的上传或下载任务线程上限。
     * 过高可能导致资源争用和网络拥塞，过低则无法充分利用带宽和多线程优势。
     */
    private int maxConcurrentTransfers = 3;
    
    /**
     * 最大空闲连接数，默认5。
     * OkHttp 连接池中保持的可复用空闲连接数量上限。
     * 保持连接复用可减少握手开销，过多会占用资源，过少会频繁重建连接导致延迟。
     */
    private int maxIdleConnections = 5;
    
    /**
     * 连接保活时间（分钟），默认5分钟。
     * 空闲连接在连接池中保存可复用的最长时长。
     * 超出后空闲连接将被清理，避免长时间占用系统资源。
     */
    private long keepAliveDurationMinutes = 5;
    
    /**
     * 重试次数，默认3次。
     * 当上传或下载操作失败且实现逻辑支持时，允许重新尝试的最大次数。
     * 当前默认 RetryManager 策略使用指数退避和抖动，若需简单重试，可参考此参数。
     */
    private int retryCount = 3;
    
    /**
     * 重试间隔（毫秒），默认1000毫秒。
     * 在连续失败后等待再发起下次尝试的时间间隔。
     * 配合指数退避或抖动策略，可有效减少高并发下的重试冲击。
     */
    private long retryIntervalMs = 1000;

    /**
     * 获取服务器URL
     *
     * @return 服务器基础URL
     */
    public String getServerUrl() {
        validateConfig();
        return auth.getServerUrl();
    }

    /**
     * 验证完整配置
     *
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public void validateConfig() {
        if (auth == null) {
            throw new IllegalStateException("认证配置不能为空");
        }

        // 验证认证配置
        auth.validate();

        // 验证其他配置
        if (chunkSize <= 0) {
            throw new IllegalStateException("分片大小必须大于0");
        }
        if (retryCount < 0) {
            throw new IllegalStateException("重试次数不能为负数");
        }
        if (retryIntervalMs < 0) {
            throw new IllegalStateException("重试间隔不能为负数");
        }
        if (maxConcurrentTransfers <= 0) {
            throw new IllegalStateException("最大并发传输数必须大于0");
        }
    }
} 