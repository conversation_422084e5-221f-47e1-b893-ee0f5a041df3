package com.sdesrd.filetransfer.client.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClientConfigBuilder 单元测试
 * 验证配置构建器的功能
 * 
 * <AUTHOR> SDK
 * @version 2.0.0
 */
@DisplayName("客户端配置构建器测试")
class ClientConfigBuilderTest {
    
    @Test
    @DisplayName("基本配置构建")
    void testBasicConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("example.com")
                .serverPort(9090)
                .auth("testUser", "testSecret")
                .build();
        
        assertEquals("example.com", config.getAuth().getServerAddr());
        assertEquals(9090, config.getAuth().getServerPort());
        assertEquals("testUser", config.getAuth().getUser());
        assertEquals("testSecret", config.getAuth().getSecretKey());
        assertEquals("http://example.com:9090", config.getServerUrl());
    }
    
    @Test
    @DisplayName("HTTPS配置构建")
    void testHttpsConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("secure.example.com")
                .serverPort(443)
                .useHttps()
                .auth("testUser", "testSecret")
                .build();
        
        assertTrue(config.getAuth().isUseHttps());
        assertEquals("https://secure.example.com:443", config.getServerUrl());
    }
    
    @Test
    @DisplayName("上下文路径配置构建")
    void testContextPathConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .contextPath("api/v1")
                .auth("testUser", "testSecret")
                .build();
        
        assertEquals("api/v1", config.getAuth().getContextPath());
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
    }
    
    @Test
    @DisplayName("完整配置构建")
    void testFullConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("api.example.com")
                .serverPort(8443)
                .contextPath("filetransfer")
                .useHttps()
                .auth("admin", "super-secret-key")
                .chunkSize(2 * 1024 * 1024) // 2MB
                .maxConcurrentTransfers(10)
                .retry(5, 2000)
                .build();
        
        // 验证认证配置
        assertEquals("api.example.com", config.getAuth().getServerAddr());
        assertEquals(8443, config.getAuth().getServerPort());
        assertEquals("filetransfer", config.getAuth().getContextPath());
        assertTrue(config.getAuth().isUseHttps());
        assertEquals("admin", config.getAuth().getUser());
        assertEquals("super-secret-key", config.getAuth().getSecretKey());
        
        // 验证其他配置
        assertEquals(2 * 1024 * 1024, config.getChunkSize());
        assertEquals(10, config.getMaxConcurrentTransfers());
        assertEquals(5, config.getRetryCount());
        assertEquals(2000, config.getRetryIntervalMs());
        
        // 验证URL构建
        assertEquals("https://api.example.com:8443/filetransfer", config.getServerUrl());
    }
    
    @Test
    @DisplayName("默认配置创建")
    void testDefaultConfig() {
        ClientConfig config = ClientConfigBuilder.defaultConfig("user", "secret");
        
        assertEquals("localhost", config.getAuth().getServerAddr());
        assertEquals(49011, config.getAuth().getServerPort());
        assertEquals("user", config.getAuth().getUser());
        assertEquals("secret", config.getAuth().getSecretKey());
        assertFalse(config.getAuth().isUseHttps());
        assertEquals("", config.getAuth().getContextPath());
    }
    
    @Test
    @DisplayName("本地开发配置创建")
    void testLocalConfig() {
        ClientConfig config = ClientConfigBuilder.localConfig("dev", "dev-secret");
        
        assertEquals("localhost", config.getAuth().getServerAddr());
        assertEquals(49011, config.getAuth().getServerPort());
        assertEquals("dev", config.getAuth().getUser());
        assertEquals("dev-secret", config.getAuth().getSecretKey());
        assertEquals(1024 * 1024, config.getChunkSize());
        assertEquals(3, config.getMaxConcurrentTransfers());
        assertEquals(3, config.getRetryCount());
        assertEquals(1000, config.getRetryIntervalMs());
    }
    
    @Test
    @DisplayName("生产环境配置创建")
    void testProductionConfig() {
        ClientConfig config = ClientConfigBuilder.productionConfig(
                "prod.example.com", 443, "prod-user", "prod-secret");
        
        assertEquals("prod.example.com", config.getAuth().getServerAddr());
        assertEquals(443, config.getAuth().getServerPort());
        assertTrue(config.getAuth().isUseHttps());
        assertEquals("prod-user", config.getAuth().getUser());
        assertEquals("prod-secret", config.getAuth().getSecretKey());
        assertEquals(2 * 1024 * 1024, config.getChunkSize());
        assertEquals(5, config.getMaxConcurrentTransfers());
        assertEquals(5, config.getRetryCount());
        assertEquals(2000, config.getRetryIntervalMs());
    }
    
    @Test
    @DisplayName("构建时验证失败")
    void testBuildValidationFailure() {
        // 缺少用户名
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .serverAddr("example.com")
                    .build();
        });
        
        // 缺少密钥
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .serverAddr("example.com")
                    .auth("user", "")
                    .build();
        });
        
        // 无效的分片大小
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .auth("user", "secret")
                    .chunkSize(-1)
                    .build();
        });
        
        // 无效的并发传输数
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .auth("user", "secret")
                    .maxConcurrentTransfers(0)
                    .build();
        });
    }
    
    @Test
    @DisplayName("链式调用验证")
    void testMethodChaining() {
        ClientConfigBuilder builder = ClientConfigBuilder.create();
        
        // 验证所有方法都返回构建器实例，支持链式调用
        assertSame(builder, builder.serverAddr("test"));
        assertSame(builder, builder.serverPort(49011));
        assertSame(builder, builder.contextPath("api"));
        assertSame(builder, builder.useHttps());
        assertSame(builder, builder.auth("user", "secret"));
        assertSame(builder, builder.chunkSize(1024));
        assertSame(builder, builder.maxConcurrentTransfers(5));
        assertSame(builder, builder.retry(3, 1000));
    }
}
